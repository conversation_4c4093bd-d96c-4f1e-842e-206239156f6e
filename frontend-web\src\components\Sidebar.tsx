import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Collapse,
  Box,
  Typography,
  Divider,
  useTheme,
  alpha,
  Tooltip,
  IconButton
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  Pets as AnimalsIcon,
  LocalHospital as HealthIcon,
  Favorite as BreedingIcon,
  Restaurant as FeedingIcon,
  AttachMoney as FinancialIcon,
  Inventory as InventoryIcon,
  Store as CommercialIcon,
  Assessment as ReportsIcon,
  MenuBook as ResourcesIcon,
  Settings as SettingsIcon,
  Security as ComplianceIcon,
  Analytics as AnalyticsIcon,
  ExpandLess,
  ExpandMore,
  ChevronLeft,
  ChevronRight,
  Lock
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import { useThemeContext } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import { useSubscription } from '../contexts/SubscriptionContext';
import { ROUTES } from '../constants/routes';
import BetaFeatureGate from './beta/BetaFeatureGate';
import BetaUpgradeButton from './beta/BetaUpgradeButton';
import AgriIntelBrand from './branding/AgriIntelBrand';
import './SidebarFix.css';

interface SidebarProps {
  isOpen: boolean;
  onToggle: () => void;
  isMobile: boolean;
}

interface SubModule {
  name: string;
  path: string;
  icon?: React.ReactNode;
}

interface ModuleItem {
  name: string;
  path: string;
  icon: React.ReactNode;
  subModules?: SubModule[];
  betaAccess?: boolean; // Whether this module is available in beta
  premiumOnly?: boolean; // Whether this module requires premium subscription
  feature?: string; // Feature name for subscription checking
}

const Sidebar: React.FC<SidebarProps> = ({ isOpen, onToggle, isMobile }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const theme = useTheme();
  const { currentColor, availableColors } = useThemeContext();
  const { translate } = useLanguage();
  const { subscription, checkFeatureAccess } = useSubscription();
  const [expandedModules, setExpandedModules] = useState<string[]>([]);

  const themeColor = availableColors[currentColor];

  const handleModuleClick = (module: ModuleItem) => {
    // Check if module is accessible in current subscription
    if (module.premiumOnly && subscription?.plan?.name === 'beta') {
      // Show upgrade prompt for premium-only modules
      return;
    }

    if (module.feature && !checkFeatureAccess(module.feature)) {
      // Feature not accessible in current plan
      return;
    }

    if (module.subModules && module.subModules.length > 0) {
      // Toggle expansion
      setExpandedModules(prev =>
        prev.includes(module.name)
          ? prev.filter(name => name !== module.name)
          : [...prev, module.name]
      );
    } else {
      // Navigate directly
      navigate(module.path);
      if (isMobile) {
        onToggle();
      }
    }
  };

  const handleSubModuleClick = (subModule: SubModule) => {
    navigate(subModule.path);
    if (isMobile) {
      onToggle();
    }
  };

  const isActive = (path: string) => {
    return location.pathname === path || location.pathname.startsWith(path + '/');
  };

  // Check if we're in beta mode
  const isBetaMode = location.pathname.startsWith('/beta-dashboard');

  // Define all modules
  const allModuleItems: ModuleItem[] = [
    {
      name: translate('nav.dashboard'),
      path: isBetaMode ? '/beta-dashboard' : ROUTES.DASHBOARD,
      icon: <DashboardIcon />,
      betaAccess: true
    },
    {
      name: translate('nav.animals'),
      path: isBetaMode ? '/beta-dashboard/animals' : ROUTES.ANIMALS,
      icon: <AnimalsIcon />,
      betaAccess: true,
      feature: 'animal_management',
      subModules: [
        { name: 'Dashboard', path: isBetaMode ? '/beta-dashboard/animals' : `${ROUTES.ANIMALS}` },
        { name: 'Animal Tracking', path: isBetaMode ? '/beta-dashboard/animals/tracking' : `${ROUTES.ANIMALS}/tracking` },
        { name: 'Animal Profiles', path: isBetaMode ? '/beta-dashboard/animals/profiles' : `${ROUTES.ANIMALS}/profiles` },
        { name: 'Animal Records', path: isBetaMode ? '/beta-dashboard/animals/records' : `${ROUTES.ANIMALS}/records` },
        { name: 'Growth Tracking', path: isBetaMode ? '/beta-dashboard/animals/growth' : `${ROUTES.ANIMALS}/growth` },
        { name: 'RFID Tracking', path: isBetaMode ? '/beta-dashboard/animals/rfid' : `${ROUTES.ANIMALS}/rfid` },
        { name: 'Asset Management', path: isBetaMode ? '/beta-dashboard/animals/asset-management' : `${ROUTES.ANIMALS}/asset-management` },
        { name: 'Genealogy', path: isBetaMode ? '/beta-dashboard/animals/genealogy' : `${ROUTES.ANIMALS}/genealogy` },
        { name: 'Health Prediction', path: isBetaMode ? '/beta-dashboard/animals/health-prediction' : `${ROUTES.ANIMALS}/health-prediction` },
        { name: 'Retirement Tracking', path: isBetaMode ? '/beta-dashboard/animals/retirement' : `${ROUTES.ANIMALS}/retirement` }
      ]
    },
    {
      name: translate('nav.health'),
      path: isBetaMode ? '/beta-dashboard/health' : ROUTES.HEALTH,
      icon: <HealthIcon />,
      betaAccess: true,
      feature: 'health_management',
      subModules: [
        { name: 'Dashboard', path: isBetaMode ? '/beta-dashboard/health' : `${ROUTES.HEALTH}` },
        { name: 'Health Records', path: isBetaMode ? '/beta-dashboard/health/records' : `${ROUTES.HEALTH}/records` },
        { name: 'Treatments', path: isBetaMode ? '/beta-dashboard/health/treatments' : `${ROUTES.HEALTH}/treatments` },
        { name: 'Vaccinations', path: isBetaMode ? '/beta-dashboard/health/vaccinations' : `${ROUTES.HEALTH}/vaccinations` },
        { name: 'Appointments', path: isBetaMode ? '/beta-dashboard/health/appointments' : `${ROUTES.HEALTH}/appointments` },
        { name: 'Diseases', path: isBetaMode ? '/beta-dashboard/health/diseases' : `${ROUTES.HEALTH}/diseases` }
      ]
    },
    {
      name: translate('nav.resources'),
      path: isBetaMode ? '/beta-dashboard/resources' : ROUTES.RESOURCES,
      icon: <ResourcesIcon />,
      betaAccess: true,
      feature: 'resources',
      subModules: [
        { name: 'Dashboard', path: isBetaMode ? '/beta-dashboard/resources' : `${ROUTES.RESOURCES}` },
        { name: 'Documentation', path: isBetaMode ? '/beta-dashboard/resources/documentation' : `${ROUTES.RESOURCES}/documentation` },
        { name: 'Training', path: isBetaMode ? '/beta-dashboard/resources/training' : `${ROUTES.RESOURCES}/training` },
        { name: 'Support', path: isBetaMode ? '/beta-dashboard/resources/support' : `${ROUTES.RESOURCES}/support` },
        { name: 'Guidelines', path: isBetaMode ? '/beta-dashboard/resources/guidelines' : `${ROUTES.RESOURCES}/guidelines` },
        { name: 'Downloads', path: isBetaMode ? '/beta-dashboard/resources/downloads' : `${ROUTES.RESOURCES}/downloads` },
        { name: 'Government Resources', path: isBetaMode ? '/beta-dashboard/resources/government' : `${ROUTES.RESOURCES}/government` },
        { name: 'Maintenance', path: isBetaMode ? '/beta-dashboard/resources/maintenance' : `${ROUTES.RESOURCES}/maintenance` },
        { name: 'Utilization', path: isBetaMode ? '/beta-dashboard/resources/utilization' : `${ROUTES.RESOURCES}/utilization` }
      ]
    },
    {
      name: translate('nav.settings'),
      path: isBetaMode ? '/beta-dashboard/settings' : ROUTES.SETTINGS,
      icon: <SettingsIcon />,
      betaAccess: true,
      subModules: [
        { name: 'Dashboard', path: isBetaMode ? '/beta-dashboard/settings' : `${ROUTES.SETTINGS}` },
        { name: 'Theme Settings', path: isBetaMode ? '/beta-dashboard/settings/theme' : `${ROUTES.SETTINGS}/theme` },
        { name: 'User Management', path: isBetaMode ? '/beta-dashboard/settings/users' : `${ROUTES.SETTINGS}/users` },
        { name: 'Database Settings', path: isBetaMode ? '/beta-dashboard/settings/database' : `${ROUTES.SETTINGS}/database` },
        { name: 'Backup Settings', path: isBetaMode ? '/beta-dashboard/settings/backup' : `${ROUTES.SETTINGS}/backup` }
      ]
    },
    // LIVE-only modules (not shown in BETA)
    {
      name: translate('nav.breeding'),
      path: ROUTES.BREEDING,
      icon: <BreedingIcon />,
      betaAccess: false,
      feature: 'breeding_management',
      subModules: [
        { name: 'Dashboard', path: `${ROUTES.BREEDING}` },
        { name: 'Breeding Records', path: `${ROUTES.BREEDING}/records` },
        { name: 'Breeding Schedule', path: `${ROUTES.BREEDING}/schedule` },
        { name: 'Pregnancy Tracking', path: `${ROUTES.BREEDING}/pregnancy` },
        { name: 'Birth Records', path: `${ROUTES.BREEDING}/births` },
        { name: 'Birth Predictions', path: `${ROUTES.BREEDING}/predictions` },
        { name: 'Heat Calendar', path: `${ROUTES.BREEDING}/calendar` }
      ]
    },
    {
      name: translate('nav.feeding'),
      path: ROUTES.FEED,
      icon: <FeedingIcon />,
      betaAccess: false,
      feature: 'feeding_management',
      subModules: [
        { name: 'Dashboard', path: `${ROUTES.FEED}` },
        { name: 'Feeding Records', path: `${ROUTES.FEED}/records` },
        { name: 'Feeding Schedules', path: `${ROUTES.FEED}/schedules` },
        { name: 'Feeding Plans', path: `${ROUTES.FEED}/plans` },
        { name: 'Nutrition', path: `${ROUTES.FEED}/nutrition` },
        { name: 'Inventory', path: `${ROUTES.FEED}/inventory` },
        { name: 'Suppliers', path: `${ROUTES.FEED}/suppliers` }
      ]
    },
    {
      name: translate('nav.financial'),
      path: ROUTES.FINANCIAL,
      icon: <FinancialIcon />,
      betaAccess: true, // Now available in BETA with basic features
      feature: 'financial_basic',
      subModules: [
        { name: 'Dashboard', path: `${ROUTES.FINANCIAL}` },
        { name: 'Basic Income', path: `${ROUTES.FINANCIAL}/income` },
        { name: 'Basic Expenses', path: `${ROUTES.FINANCIAL}/expenses` },
        { name: 'Simple Reports', path: `${ROUTES.FINANCIAL}/reports` },
        // Premium features for Professional/Enterprise
        { name: 'Advanced Analytics', path: `${ROUTES.FINANCIAL}/analytics`, premiumOnly: true },
        { name: 'Budget Planning', path: `${ROUTES.FINANCIAL}/budgets`, premiumOnly: true },
        { name: 'ROI Analysis', path: `${ROUTES.FINANCIAL}/roi`, premiumOnly: true },
        { name: 'Forecasting', path: `${ROUTES.FINANCIAL}/forecast`, premiumOnly: true },
        { name: 'Invoicing', path: `${ROUTES.FINANCIAL}/invoices`, premiumOnly: true }
      ]
    },
    {
      name: translate('nav.inventory'),
      path: ROUTES.INVENTORY,
      icon: <InventoryIcon />,
      betaAccess: false,
      premiumOnly: true,
      feature: 'inventory_management',
      subModules: [
        { name: 'Dashboard', path: `${ROUTES.INVENTORY}` }
      ]
    },
    {
      name: translate('nav.commercial'),
      path: ROUTES.COMMERCIAL,
      icon: <CommercialIcon />,
      betaAccess: false,
      premiumOnly: true,
      feature: 'commercial_operations',
      subModules: [
        { name: 'Dashboard', path: `${ROUTES.COMMERCIAL}` },
        { name: 'Marketplace', path: `${ROUTES.COMMERCIAL}/marketplace` },
        { name: 'Auctions', path: `${ROUTES.COMMERCIAL}/auctions` },
        { name: 'BKB Auctions', path: `${ROUTES.COMMERCIAL}/bkb-auctions` },
        { name: 'Orders', path: `${ROUTES.COMMERCIAL}/orders` },
        { name: 'Suppliers', path: `${ROUTES.COMMERCIAL}/suppliers` },
        { name: 'Pricing', path: `${ROUTES.COMMERCIAL}/pricing` },
        { name: 'Price Updates', path: `${ROUTES.COMMERCIAL}/price-updates` }
      ]
    },
    {
      name: translate('nav.reports'),
      path: ROUTES.REPORTS,
      icon: <ReportsIcon />,
      betaAccess: false,
      feature: 'basic_reports',
      subModules: [
        { name: 'Dashboard', path: `${ROUTES.REPORTS}` },
        { name: 'Analysis', path: `${ROUTES.REPORTS}/analysis` },
        { name: 'Performance', path: `${ROUTES.REPORTS}/performance` },
        { name: 'Health Reports', path: `${ROUTES.REPORTS}/health` },
        { name: 'Market Reports', path: `${ROUTES.REPORTS}/market` },
        { name: 'Financial Reports', path: `${ROUTES.REPORTS}/financial` },
        { name: 'Feeding Reports', path: `${ROUTES.REPORTS}/feeding` },
        { name: 'Breeding Reports', path: `${ROUTES.REPORTS}/breeding` },
        { name: 'Predictive Analysis', path: `${ROUTES.REPORTS}/predictive` },
        { name: 'Custom Reports', path: `${ROUTES.REPORTS}/custom` }
      ]
    },
    {
      name: translate('nav.analytics'),
      path: ROUTES.ANALYTICS,
      icon: <AnalyticsIcon />,
      betaAccess: false,
      premiumOnly: true,
      feature: 'advanced_analytics',
      subModules: [
        { name: 'Business Analysis', path: `${ROUTES.ANALYTICS}` },
        { name: 'Business Strategy', path: `${ROUTES.ANALYTICS}/strategy` },
        { name: 'Business Predictions', path: `${ROUTES.ANALYTICS}/predictions` }
      ]
    },
    {
      name: translate('nav.compliance'),
      path: ROUTES.COMPLIANCE,
      icon: <ComplianceIcon />,
      betaAccess: false,
      premiumOnly: true,
      feature: 'compliance_management',
      subModules: [
        { name: 'Dashboard', path: `${ROUTES.COMPLIANCE}` },
        { name: 'Certifications', path: `${ROUTES.COMPLIANCE}/certifications` },
        { name: 'Inspections', path: `${ROUTES.COMPLIANCE}/inspections` },
        { name: 'Documents', path: `${ROUTES.COMPLIANCE}/documents` }
      ]
    }
  ];

  // Filter modules based on mode
  const moduleItems = isBetaMode
    ? allModuleItems.filter(module => module.betaAccess === true)
    : allModuleItems;

  // Auto-expand current module
  useEffect(() => {
    const currentPath = location.pathname;
    const currentModule = moduleItems.find(module =>
      currentPath.startsWith(module.path) ||
      module.subModules?.some(sub => currentPath.startsWith(sub.path))
    );

    if (currentModule && !expandedModules.includes(currentModule.name)) {
      setExpandedModules(prev => [...prev, currentModule.name]);
    }
  }, [location.pathname, moduleItems, expandedModules]);

  const drawerWidth = isOpen ? 280 : 64;

  const listItemVariants = {
    open: { opacity: 1, x: 0, transition: { duration: 0.2 } },
    closed: { opacity: 0, x: -20, transition: { duration: 0.2 } }
  };

  return (
    <Drawer
      variant={isMobile ? "temporary" : "permanent"}
      open={isMobile ? isOpen : true}
      onClose={isMobile ? onToggle : undefined}
      sx={{
        width: drawerWidth,
        flexShrink: 0,
        '& .MuiDrawer-paper': {
          width: drawerWidth,
          boxSizing: 'border-box',
          background: `linear-gradient(180deg, ${themeColor.primary}, ${themeColor.secondary})`,
          borderRight: 'none',
          transition: 'width 0.3s ease-in-out',
          overflowX: 'hidden',
          '&::-webkit-scrollbar': {
            width: '6px',
          },
          '&::-webkit-scrollbar-track': {
            background: alpha(theme.palette.common.white, 0.1),
          },
          '&::-webkit-scrollbar-thumb': {
            background: alpha(theme.palette.common.white, 0.3),
            borderRadius: '3px',
            '&:hover': {
              background: alpha(theme.palette.common.white, 0.5),
            },
          },
        },
      }}
    >
      {/* Header */}
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: isOpen ? 'space-between' : 'center',
          p: 2,
          minHeight: 64,
          borderBottom: `1px solid ${alpha(theme.palette.common.white, 0.1)}`,
        }}
      >
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3 }}
          >
            <Box>
              <AgriIntelBrand
                variant="compact"
                size="small"
                showSlogan={false}
                color="white"
                orientation="vertical"
                sx={{ mb: 0.5 }}
              />
              {subscription?.plan?.name === 'beta' && (
                <Typography
                  variant="caption"
                  sx={{
                    color: alpha(theme.palette.common.white, 0.8),
                    fontSize: '0.7rem',
                    display: 'block',
                    textAlign: 'center'
                  }}
                >
                  Beta Version
                </Typography>
              )}
            </Box>
          </motion.div>
        )}

        {!isMobile && (
          <IconButton
            onClick={onToggle}
            sx={{
              color: 'white',
              '&:hover': {
                backgroundColor: alpha(theme.palette.common.white, 0.1),
              },
            }}
          >
            {isOpen ? <ChevronLeft /> : <ChevronRight />}
          </IconButton>
        )}
      </Box>

      {/* Navigation List */}
      <List sx={{ pt: 1, pb: 1 }}>
        {moduleItems.map((module, index) => {
          const isLocked = module.premiumOnly && subscription?.plan?.name === 'beta';

          return (
            <React.Fragment key={module.name}>
              <ListItem disablePadding>
                {isLocked ? (
                  <BetaFeatureGate
                    feature={module.feature || 'premium_module'}
                    showPreview={false}
                    upgradeMessage={`Upgrade to access ${module.name}`}
                  >
                    <Tooltip
                      title={!isOpen ? `${module.name} (Premium)` : ''}
                      placement="right"
                      arrow
                    >
                      <ListItemButton
                        disabled
                        sx={{
                          minHeight: 48,
                          px: 2.5,
                          py: 1,
                          mx: 1,
                          mb: 0.5,
                          borderRadius: 2,
                          color: alpha(theme.palette.common.white, 0.5),
                          backgroundColor: 'transparent',
                          opacity: 0.6,
                          cursor: 'not-allowed',
                          '&:hover': {
                            backgroundColor: alpha(theme.palette.common.white, 0.05),
                          },
                          transition: 'all 0.2s ease-in-out',
                        }}
                      >
                        <ListItemIcon
                          sx={{
                            minWidth: 0,
                            mr: isOpen ? 2 : 'auto',
                            justifyContent: 'center',
                            color: alpha(theme.palette.common.white, 0.5),
                          }}
                        >
                          {module.icon}
                        </ListItemIcon>

                        {isOpen && (
                          <motion.div
                            variants={listItemVariants}
                            initial="closed"
                            animate="open"
                            style={{ display: 'flex', alignItems: 'center', width: '100%' }}
                          >
                            <ListItemText
                              primary={module.name}
                              sx={{
                                '& .MuiListItemText-primary': {
                                  fontSize: '0.9rem',
                                  fontWeight: 500,
                                },
                              }}
                            />
                            <Lock sx={{ fontSize: 16, ml: 1 }} />
                          </motion.div>
                        )}
                      </ListItemButton>
                    </Tooltip>
                  </BetaFeatureGate>
                ) : (
                  <Tooltip
                    title={!isOpen ? module.name : ''}
                    placement="right"
                    arrow
                  >
                    <ListItemButton
                      onClick={() => handleModuleClick(module)}
                      sx={{
                        minHeight: 48,
                        px: 2.5,
                        py: 1,
                        mx: 1,
                        mb: 0.5,
                        borderRadius: 2,
                        color: 'white',
                        backgroundColor: isActive(module.path)
                          ? alpha(theme.palette.common.white, 0.2)
                          : 'transparent',
                        '&:hover': {
                          backgroundColor: alpha(theme.palette.common.white, 0.15),
                          transform: 'translateX(4px)',
                        },
                        transition: 'all 0.2s ease-in-out',
                      }}
                    >
                      <ListItemIcon
                        sx={{
                          minWidth: 0,
                          mr: isOpen ? 2 : 'auto',
                          justifyContent: 'center',
                          color: 'white',
                        }}
                      >
                        {module.icon}
                      </ListItemIcon>

                      {isOpen && (
                        <motion.div
                          variants={listItemVariants}
                          initial="closed"
                          animate="open"
                          style={{ display: 'flex', alignItems: 'center', width: '100%' }}
                        >
                          <ListItemText
                            primary={module.name}
                            sx={{
                              '& .MuiListItemText-primary': {
                                fontSize: '0.9rem',
                                fontWeight: 500,
                              },
                            }}
                          />
                          {module.subModules && module.subModules.length > 0 && (
                            expandedModules.includes(module.name) ? <ExpandLess /> : <ExpandMore />
                          )}
                        </motion.div>
                      )}
                    </ListItemButton>
                  </Tooltip>
                )}
              </ListItem>

            {/* Sub-modules */}
            {module.subModules && module.subModules.length > 0 && (
              <AnimatePresence>
                {expandedModules.includes(module.name) && isOpen && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    exit={{ opacity: 0, height: 0 }}
                    transition={{ duration: 0.2 }}
                  >
                    <Collapse in={expandedModules.includes(module.name)} timeout="auto" unmountOnExit>
                      <List component="div" disablePadding>
                        {module.subModules.map((subModule) => (
                          <ListItem key={subModule.name} disablePadding>
                            <ListItemButton
                              onClick={() => handleSubModuleClick(subModule)}
                              sx={{
                                pl: 6,
                                pr: 2.5,
                                py: 0.8,
                                mx: 1,
                                mb: 0.3,
                                borderRadius: 1.5,
                                color: alpha(theme.palette.common.white, 0.9),
                                backgroundColor: isActive(subModule.path)
                                  ? alpha(theme.palette.common.white, 0.15)
                                  : 'transparent',
                                '&:hover': {
                                  backgroundColor: alpha(theme.palette.common.white, 0.1),
                                  transform: 'translateX(4px)',
                                },
                                transition: 'all 0.2s ease-in-out',
                              }}
                            >
                              <ListItemText
                                primary={subModule.name}
                                sx={{
                                  '& .MuiListItemText-primary': {
                                    fontSize: '0.8rem',
                                    fontWeight: isActive(subModule.path) ? 600 : 400,
                                  },
                                }}
                              />
                            </ListItemButton>
                          </ListItem>
                        ))}
                      </List>
                    </Collapse>
                  </motion.div>
                )}
              </AnimatePresence>
            )}

              {/* Add divider after certain modules for better organization */}
              {(index === 0 || index === 4 || index === 7 || index === 10) && (
                <Divider
                  sx={{
                    my: 1,
                    mx: 2,
                    borderColor: alpha(theme.palette.common.white, 0.1)
                  }}
                />
              )}
            </React.Fragment>
          );
        })}
      </List>

      {/* Beta Upgrade Button */}
      {subscription?.plan?.name === 'beta' && isOpen && (
        <Box sx={{ p: 2, mt: 'auto' }}>
          <BetaUpgradeButton variant="sidebar" size="medium" />
        </Box>
      )}
    </Drawer>
  );
};

export default Sidebar;