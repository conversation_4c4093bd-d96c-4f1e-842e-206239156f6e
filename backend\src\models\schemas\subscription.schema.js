/**
 * Subscription Schema
 * 
 * Defines the MongoDB schema for user subscriptions and billing
 */

const mongoose = require('mongoose');
const Schema = mongoose.Schema;

// Subscription Plan Schema
const subscriptionPlanSchema = new Schema({
  name: {
    type: String,
    required: true,
    enum: ['beta', 'professional', 'cooperative'],
    unique: true
  },
  displayName: {
    type: String,
    required: true
  },
  description: {
    type: String,
    required: true
  },
  price: {
    monthly: {
      type: Number,
      required: true,
      min: 0
    },
    yearly: {
      type: Number,
      required: true,
      min: 0
    },
    currency: {
      type: String,
      default: 'ZAR'
    }
  },
  features: {
    maxAnimals: {
      type: Number,
      default: -1 // -1 means unlimited
    },
    maxUsers: {
      type: Number,
      default: 1
    },
    historicalDataMonths: {
      type: Number,
      default: -1 // -1 means unlimited
    },
    maxReports: {
      type: Number,
      default: -1
    },
    maxAlerts: {
      type: Number,
      default: -1
    },
    aiAgentMode: {
      type: Boolean,
      default: false
    },
    advancedAnalytics: {
      type: Boolean,
      default: false
    },
    financialIntelligence: {
      type: Boolean,
      default: false
    },
    apiAccess: {
      type: Boolean,
      default: false
    },
    prioritySupport: {
      type: Boolean,
      default: false
    },
    customReports: {
      type: Boolean,
      default: false
    },
    dataExport: {
      formats: [{
        type: String,
        enum: ['csv', 'excel', 'pdf', 'api']
      }],
      default: ['csv']
    },
    governmentIntegration: {
      type: Boolean,
      default: true
    },
    weatherIntegration: {
      type: Boolean,
      default: true
    },
    mobileApp: {
      type: Boolean,
      default: true
    }
  },
  isActive: {
    type: Boolean,
    default: true
  },
  sortOrder: {
    type: Number,
    default: 0
  }
}, {
  timestamps: true
});

// User Subscription Schema
const userSubscriptionSchema = new Schema({
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  planId: {
    type: Schema.Types.ObjectId,
    ref: 'SubscriptionPlan',
    required: true
  },
  status: {
    type: String,
    enum: ['active', 'cancelled', 'expired', 'trial', 'suspended'],
    default: 'trial'
  },
  startDate: {
    type: Date,
    required: true,
    default: Date.now
  },
  endDate: {
    type: Date,
    required: true
  },
  trialEndDate: {
    type: Date
  },
  billingCycle: {
    type: String,
    enum: ['monthly', 'yearly'],
    default: 'monthly'
  },
  paymentMethod: {
    type: String,
    enum: ['credit_card', 'debit_card', 'bank_transfer', 'paypal', 'stripe'],
    default: 'credit_card'
  },
  paymentDetails: {
    stripeCustomerId: String,
    stripeSubscriptionId: String,
    lastPaymentDate: Date,
    nextPaymentDate: Date,
    paymentFailures: {
      type: Number,
      default: 0
    }
  },
  usage: {
    currentAnimals: {
      type: Number,
      default: 0
    },
    currentUsers: {
      type: Number,
      default: 1
    },
    monthlyAlerts: {
      type: Number,
      default: 0
    },
    monthlyReports: {
      type: Number,
      default: 0
    },
    lastResetDate: {
      type: Date,
      default: Date.now
    }
  },
  conversionTracking: {
    betaStartDate: Date,
    conversionDate: Date,
    conversionSource: {
      type: String,
      enum: ['limit_reached', 'feature_preview', 'trial_offer', 'referral', 'marketing']
    },
    interactionHistory: [{
      action: {
        type: String,
        enum: ['feature_preview', 'upgrade_prompt', 'limit_warning', 'trial_started']
      },
      timestamp: {
        type: Date,
        default: Date.now
      },
      metadata: Schema.Types.Mixed
    }]
  },
  discounts: [{
    code: String,
    type: {
      type: String,
      enum: ['percentage', 'fixed_amount', 'free_months']
    },
    value: Number,
    appliedDate: Date,
    expiryDate: Date
  }],
  cancellation: {
    requestedDate: Date,
    effectiveDate: Date,
    reason: String,
    feedback: String
  }
}, {
  timestamps: true
});

// Feature Usage Tracking Schema
const featureUsageSchema = new Schema({
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  feature: {
    type: String,
    required: true
  },
  action: {
    type: String,
    required: true
  },
  timestamp: {
    type: Date,
    default: Date.now
  },
  metadata: Schema.Types.Mixed,
  sessionId: String,
  userAgent: String,
  ipAddress: String
}, {
  timestamps: true
});

// Beta Conversion Metrics Schema
const betaConversionMetricsSchema = new Schema({
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  province: {
    type: String,
    enum: ['eastern-cape', 'free-state', 'gauteng', 'kwazulu-natal', 'limpopo', 'mpumalanga', 'northern-cape', 'north-west', 'western-cape']
  },
  farmSize: {
    type: String,
    enum: ['small', 'medium', 'large', 'commercial']
  },
  livestockTypes: [String],
  engagementScore: {
    type: Number,
    default: 0,
    min: 0,
    max: 100
  },
  conversionProbability: {
    type: Number,
    default: 0,
    min: 0,
    max: 1
  },
  lastActivity: {
    type: Date,
    default: Date.now
  },
  keyMetrics: {
    daysActive: {
      type: Number,
      default: 0
    },
    featuresUsed: {
      type: Number,
      default: 0
    },
    recordsCreated: {
      type: Number,
      default: 0
    },
    limitsHit: {
      type: Number,
      default: 0
    },
    previewsViewed: {
      type: Number,
      default: 0
    }
  }
}, {
  timestamps: true
});

// Indexes for performance
subscriptionPlanSchema.index({ name: 1 });
subscriptionPlanSchema.index({ isActive: 1 });

userSubscriptionSchema.index({ userId: 1 });
userSubscriptionSchema.index({ status: 1 });
userSubscriptionSchema.index({ endDate: 1 });

featureUsageSchema.index({ userId: 1, timestamp: -1 });
featureUsageSchema.index({ feature: 1, timestamp: -1 });

betaConversionMetricsSchema.index({ userId: 1 });
betaConversionMetricsSchema.index({ conversionProbability: -1 });
betaConversionMetricsSchema.index({ province: 1 });

// Create models
const SubscriptionPlan = mongoose.model('SubscriptionPlan', subscriptionPlanSchema);
const UserSubscription = mongoose.model('UserSubscription', userSubscriptionSchema);
const FeatureUsage = mongoose.model('FeatureUsage', featureUsageSchema);
const BetaConversionMetrics = mongoose.model('BetaConversionMetrics', betaConversionMetricsSchema);

module.exports = {
  SubscriptionPlan,
  UserSubscription,
  FeatureUsage,
  BetaConversionMetrics
};
