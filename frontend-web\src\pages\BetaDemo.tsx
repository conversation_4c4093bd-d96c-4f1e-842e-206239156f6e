import React, { useState } from 'react';
import {
  Box,
  Container,
  <PERSON>po<PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Grid,
  Chip,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  useTheme,
  alpha,
  Paper,
  Stepper,
  Step,
  StepLabel,
  StepContent
} from '@mui/material';
import {
  Star,
  TrendingUp,
  Pets,
  LocalHospital,
  AccountBalance,
  Analytics,
  SmartToy,
  CheckCircle,
  PlayArrow,
  Upgrade,
  LocationOn,
  CloudDownload,
  Support,
  Security,
  Speed,
  MonetizationOn
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import BetaFeatureGate from '../components/beta/BetaFeatureGate';
import BetaDashboard from '../components/beta/BetaDashboard';
import EnhancedGradientBackground from '../components/common/EnhancedGradientBackground';
import AgriIntelBrand from '../components/branding/AgriIntelBrand';
import StunningBetaLanding from '../components/beta/StunningBetaLanding';

const BetaDemo: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const [showUpgradeDialog, setShowUpgradeDialog] = useState(false);
  const [activeStep, setActiveStep] = useState(0);

  const betaFeatures = [
    {
      title: 'Essential Record-Keeping',
      icon: Pets,
      description: 'Track up to 50 animals with basic health records',
      status: 'included',
      color: theme.palette.primary.main,
      benefits: [
        'Animal inventory management',
        'Basic health logging',
        'Vaccination reminders',
        'Feed schedule tracking'
      ]
    },
    {
      title: 'Government Data Integration',
      icon: LocationOn,
      description: 'Real-time access to SA government livestock data',
      status: 'included',
      color: '#10b981',
      benefits: [
        'Live auction prices',
        'Provincial vaccination schedules',
        'Veterinary service directory',
        'Weather data with livestock advisories'
      ]
    },
    {
      title: 'Basic Financial Tracking',
      icon: AccountBalance,
      description: 'Input expenses and track basic costs',
      status: 'limited',
      color: '#f59e0b',
      benefits: [
        'Expense input and categorization',
        'Basic cost tracking',
        'Simple budget monitoring',
        'Export to CSV'
      ]
    }
  ];

  const premiumFeatures = [
    {
      title: 'AI Agent Mode',
      icon: SmartToy,
      description: 'Voice-activated farm management with automation',
      savings: 'R8,000/month in time savings',
      color: '#8b5cf6',
      benefits: [
        'Voice commands for all operations',
        'One-click ordering system',
        'Automated appointment booking',
        'Smart supplier negotiations',
        'Predictive ordering',
        'Emergency response automation'
      ]
    },
    {
      title: 'Advanced Analytics',
      icon: Analytics,
      description: 'Predictive insights and performance optimization',
      savings: 'R12,000/month in improved decisions',
      color: '#3b82f6',
      benefits: [
        'Predictive health analytics',
        'Breeding optimization algorithms',
        'Market timing recommendations',
        'Climate impact analysis',
        'Regional benchmarking',
        'Performance forecasting'
      ]
    },
    {
      title: 'Financial Intelligence',
      icon: TrendingUp,
      description: 'Complete P&L analysis and tax optimization',
      savings: 'R15,000/year in tax savings',
      color: '#059669',
      benefits: [
        'Complete profit/loss analysis',
        'Tax optimization strategies',
        'Banking API integration',
        'ROI calculations',
        'Investment recommendations',
        'Financial forecasting'
      ]
    },
    {
      title: 'Priority Support',
      icon: Support,
      description: '24/7 expert support with dedicated account manager',
      savings: 'Priceless peace of mind',
      color: '#dc2626',
      benefits: [
        '24/7 expert assistance',
        'Dedicated account manager',
        'Priority response times',
        'Training sessions',
        'Custom implementation',
        'Direct phone support'
      ]
    }
  ];

  const demoSteps = [
    {
      label: 'Sign Up for Beta',
      description: 'Create your free account and get 30-day trial access',
      action: 'Get Started'
    },
    {
      label: 'Add Your Animals',
      description: 'Input up to 50 animals with basic information',
      action: 'Add Animals'
    },
    {
      label: 'Explore Government Data',
      description: 'Access real-time auction prices and vaccination schedules',
      action: 'View Data'
    },
    {
      label: 'Try Premium Features',
      description: 'Get 30-second previews of advanced capabilities',
      action: 'Preview Premium'
    },
    {
      label: 'Upgrade to Premium',
      description: 'Unlock unlimited features with first month free',
      action: 'Upgrade Now'
    }
  ];

  const handleStepClick = (step: number) => {
    setActiveStep(step);
    
    switch (step) {
      case 0:
        navigate('/register');
        break;
      case 1:
        navigate('/dashboard/animals');
        break;
      case 2:
        navigate('/dashboard');
        break;
      case 3:
        setShowUpgradeDialog(true);
        break;
      case 4:
        setShowUpgradeDialog(true);
        break;
    }
  };

  return (
    <EnhancedGradientBackground
      module="beta"
      variant="mesh"
      enableAnimation={true}
      enableParticles={true}
      opacity={0.7}
    >
      <Container maxWidth="lg" sx={{ py: 4 }}>
        {/* Hero Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <Box sx={{ textAlign: 'center', mb: 6 }}>
            <AgriIntelBrand
              variant="full"
              size="extra-large"
              showSlogan={true}
              color="primary"
              orientation="vertical"
              animated={true}
              sx={{ mb: 3 }}
            />
            <Typography variant="h5" color="text.secondary" sx={{ mb: 4, fontSize: { xs: '1.2rem', md: '1.5rem' } }}>
              The future of intelligent livestock management starts here
            </Typography>
            
            <Box sx={{ display: 'flex', justifyContent: 'center', gap: 2, mb: 4 }}>
              <Chip
                icon={<Star />}
                label="Free 30-Day Trial"
                color="primary"
                sx={{ fontSize: '1rem', py: 3, height: 'auto' }}
              />
              <Chip
                icon={<MonetizationOn />}
                label="Save R15,000+ Annually"
                color="secondary"
                sx={{ fontSize: '1rem', py: 3, height: 'auto' }}
              />
              <Chip
                icon={<Speed />}
                label="Setup in 5 Minutes"
                color="success"
                sx={{ fontSize: '1rem', py: 3, height: 'auto' }}
              />
            </Box>

            <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center' }}>
              <Button
                variant="contained"
                size="large"
                onClick={() => navigate('/dashboard')}
                sx={{
                  fontSize: '1.2rem',
                  py: 2,
                  px: 4,
                  borderRadius: 3,
                  boxShadow: `0 8px 24px ${alpha(theme.palette.primary.main, 0.3)}`
                }}
              >
                Try Beta Dashboard
              </Button>

              <Button
                variant="outlined"
                size="large"
                onClick={() => navigate('/beta-integrated')}
                sx={{
                  fontSize: '1.2rem',
                  py: 2,
                  px: 4,
                  borderRadius: 3
                }}
              >
                Try Integrated Experience
              </Button>
            </Box>
          </Box>
        </motion.div>

        {/* Demo Steps */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <Card sx={{ mb: 6, borderRadius: 3 }}>
            <CardContent sx={{ p: 4 }}>
              <Typography variant="h4" fontWeight="bold" sx={{ mb: 3, textAlign: 'center' }}>
                Get Started in 5 Simple Steps
              </Typography>
              
              <Stepper activeStep={activeStep} orientation="vertical">
                {demoSteps.map((step, index) => (
                  <Step key={step.label}>
                    <StepLabel
                      onClick={() => handleStepClick(index)}
                      sx={{ cursor: 'pointer', '& .MuiStepLabel-label': { fontSize: '1.1rem' } }}
                    >
                      {step.label}
                    </StepLabel>
                    <StepContent>
                      <Typography sx={{ mb: 2, fontSize: '1rem' }}>
                        {step.description}
                      </Typography>
                      <Button
                        variant="contained"
                        onClick={() => handleStepClick(index)}
                        sx={{ mr: 1 }}
                      >
                        {step.action}
                      </Button>
                    </StepContent>
                  </Step>
                ))}
              </Stepper>
            </CardContent>
          </Card>
        </motion.div>

        {/* Beta Features */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          <Typography variant="h4" fontWeight="bold" sx={{ mb: 4, textAlign: 'center' }}>
            What's Included in Beta
          </Typography>
          
          <Grid container spacing={3} sx={{ mb: 6 }}>
            {betaFeatures.map((feature, index) => (
              <Grid item xs={12} md={4} key={feature.title}>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Card sx={{ height: '100%', borderRadius: 3 }}>
                    <CardContent sx={{ p: 3 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                        <Box
                          sx={{
                            p: 1.5,
                            borderRadius: 2,
                            backgroundColor: alpha(feature.color, 0.1),
                            mr: 2
                          }}
                        >
                          <feature.icon sx={{ fontSize: 32, color: feature.color }} />
                        </Box>
                        <Box>
                          <Typography variant="h6" fontWeight="bold">
                            {feature.title}
                          </Typography>
                          <Chip
                            label={feature.status === 'included' ? 'Included' : 'Limited'}
                            color={feature.status === 'included' ? 'success' : 'warning'}
                            size="small"
                          />
                        </Box>
                      </Box>
                      
                      <Typography variant="body1" color="text.secondary" sx={{ mb: 2 }}>
                        {feature.description}
                      </Typography>
                      
                      <List dense>
                        {feature.benefits.map((benefit, idx) => (
                          <ListItem key={idx} sx={{ px: 0 }}>
                            <ListItemIcon sx={{ minWidth: 32 }}>
                              <CheckCircle sx={{ fontSize: 16, color: 'success.main' }} />
                            </ListItemIcon>
                            <ListItemText primary={benefit} />
                          </ListItem>
                        ))}
                      </List>
                    </CardContent>
                  </Card>
                </motion.div>
              </Grid>
            ))}
          </Grid>
        </motion.div>

        {/* Premium Features Preview */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
        >
          <Typography variant="h4" fontWeight="bold" sx={{ mb: 4, textAlign: 'center' }}>
            Premium Features (Coming Soon)
          </Typography>
          
          <Grid container spacing={3}>
            {premiumFeatures.map((feature, index) => (
              <Grid item xs={12} md={6} key={feature.title}>
                <BetaFeatureGate
                  feature={feature.title.toLowerCase().replace(/\s+/g, '_')}
                  showPreview={true}
                  upgradeMessage={`Unlock ${feature.title} to ${feature.savings}`}
                >
                  <Card sx={{ height: '100%', borderRadius: 3 }}>
                    <CardContent sx={{ p: 3 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                        <Box
                          sx={{
                            p: 1.5,
                            borderRadius: 2,
                            backgroundColor: alpha(feature.color, 0.1),
                            mr: 2
                          }}
                        >
                          <feature.icon sx={{ fontSize: 32, color: feature.color }} />
                        </Box>
                        <Typography variant="h6" fontWeight="bold">
                          {feature.title}
                        </Typography>
                      </Box>
                      
                      <Typography variant="body1" color="text.secondary" sx={{ mb: 2 }}>
                        {feature.description}
                      </Typography>
                      
                      <Alert severity="success" sx={{ mb: 2 }}>
                        <Typography variant="body2" fontWeight="bold">
                          {feature.savings}
                        </Typography>
                      </Alert>
                      
                      <List dense>
                        {feature.benefits.slice(0, 3).map((benefit, idx) => (
                          <ListItem key={idx} sx={{ px: 0 }}>
                            <ListItemIcon sx={{ minWidth: 32 }}>
                              <CheckCircle sx={{ fontSize: 16, color: 'success.main' }} />
                            </ListItemIcon>
                            <ListItemText primary={benefit} />
                          </ListItem>
                        ))}
                        {feature.benefits.length > 3 && (
                          <ListItem sx={{ px: 0 }}>
                            <ListItemText 
                              primary={`+${feature.benefits.length - 3} more features...`}
                              sx={{ fontStyle: 'italic', color: 'text.secondary' }}
                            />
                          </ListItem>
                        )}
                      </List>
                    </CardContent>
                  </Card>
                </BetaFeatureGate>
              </Grid>
            ))}
          </Grid>
        </motion.div>

        {/* CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
        >
          <Paper
            sx={{
              mt: 6,
              p: 4,
              textAlign: 'center',
              background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.primary.dark})`,
              color: 'white',
              borderRadius: 3
            }}
          >
            <Typography variant="h4" fontWeight="bold" sx={{ mb: 2 }}>
              Ready to Transform Your Farm?
            </Typography>
            <Typography variant="h6" sx={{ mb: 4, opacity: 0.9 }}>
              Join thousands of South African farmers already using AgriIntel
            </Typography>
            
            <Box sx={{ display: 'flex', justifyContent: 'center', gap: 2 }}>
              <Button
                variant="contained"
                color="secondary"
                size="large"
                onClick={() => navigate('/dashboard')}
                sx={{ fontSize: '1.1rem', py: 2, px: 4 }}
              >
                Start Free Trial
              </Button>
              <Button
                variant="outlined"
                size="large"
                onClick={() => setShowUpgradeDialog(true)}
                sx={{ 
                  fontSize: '1.1rem', 
                  py: 2, 
                  px: 4,
                  borderColor: 'white',
                  color: 'white',
                  '&:hover': {
                    borderColor: 'white',
                    backgroundColor: alpha('#ffffff', 0.1)
                  }
                }}
              >
                View Pricing
              </Button>
            </Box>
          </Paper>
        </motion.div>

        {/* Upgrade Dialog */}
        <Dialog
          open={showUpgradeDialog}
          onClose={() => setShowUpgradeDialog(false)}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle>
            <Typography variant="h5" fontWeight="bold">
              Choose Your Plan
            </Typography>
          </DialogTitle>
          <DialogContent>
            <Alert severity="success" sx={{ mb: 3 }}>
              <Typography variant="body1" fontWeight="bold">
                Special Beta Offer: First month FREE + 20% off first year!
              </Typography>
            </Alert>
            
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Card sx={{ border: `2px solid ${theme.palette.primary.main}` }}>
                  <CardContent>
                    <Typography variant="h6" fontWeight="bold" color="primary">
                      Professional
                    </Typography>
                    <Typography variant="h4" fontWeight="bold" sx={{ my: 1 }}>
                      R299<Typography component="span" variant="body2">/month</Typography>
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      Perfect for growing farms (up to 200 animals)
                    </Typography>
                    <Button variant="contained" fullWidth>
                      Choose Professional
                    </Button>
                  </CardContent>
                </Card>
              </Grid>
              
              <Grid item xs={12} md={6}>
                <Card sx={{ border: `2px solid ${theme.palette.secondary.main}` }}>
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                      <Typography variant="h6" fontWeight="bold" color="secondary">
                        Enterprise
                      </Typography>
                      <Chip label="Most Popular" size="small" color="secondary" />
                    </Box>
                    <Typography variant="h4" fontWeight="bold" sx={{ my: 1 }}>
                      R599<Typography component="span" variant="body2">/month</Typography>
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      For serious operations (unlimited animals)
                    </Typography>
                    <Button variant="contained" color="secondary" fullWidth>
                      Choose Enterprise
                    </Button>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setShowUpgradeDialog(false)}>
              Maybe Later
            </Button>
          </DialogActions>
        </Dialog>
      </Container>
    </EnhancedGradientBackground>
  );
};

export default BetaDemo;
