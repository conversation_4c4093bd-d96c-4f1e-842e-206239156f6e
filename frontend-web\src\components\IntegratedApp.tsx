import React, { useState } from 'react';
import {
  Box,
  Container,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Grid,
  Dialog,
  DialogTitle,
  DialogContent,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Alert,
  List,
  ListItem,
  ListItemText,
  ListItemIcon
} from '@mui/material';
import {
  Lock as LockIcon,
  Star as StarIcon,
  Check as CheckIcon,
  Close as CloseIcon,
  Palette as PaletteIcon
} from '@mui/icons-material';
import { useTheme, alpha } from '@mui/material/styles';
import { motion, AnimatePresence } from 'framer-motion';

import LanguageSelector from './LanguageSelector';

// Subscription Plans
const subscriptionPlans = [
  {
    id: 'free',
    name: 'Free Beta',
    price: 'R0',
    period: 'forever',
    features: [
      'Up to 50 animals',
      'Basic health tracking',
      'Simple reports',
      'Community support',
      'Mobile app access'
    ],
    limitations: [
      'Limited to 50 animals',
      'Basic features only',
      'No advanced analytics',
      'No premium support'
    ],
    color: '#4CAF50'
  },
  {
    id: 'professional',
    name: 'Professional',
    price: 'R299',
    period: 'month',
    features: [
      'Unlimited animals',
      'Advanced health monitoring',
      'Breeding management',
      'Financial tracking',
      'Custom reports',
      'Email support',
      'Data export',
      'Weather integration'
    ],
    limitations: [],
    color: '#2196F3',
    popular: true
  },
  {
    id: 'enterprise',
    name: 'Enterprise',
    price: 'R599',
    period: 'month',
    features: [
      'Everything in Professional',
      'Multi-farm management',
      'Advanced analytics',
      'AI insights',
      'Priority support',
      'Custom integrations',
      'Compliance reporting',
      'Team collaboration',
      'API access'
    ],
    limitations: [],
    color: '#FF9800'
  }
];

// Module definitions with access levels
const modules = [
  { id: 'dashboard', name: 'Dashboard', free: true, beta: true },
  { id: 'animals', name: 'Animals', free: true, beta: true, limit: 50 },
  { id: 'health', name: 'Health', free: 'basic', beta: true },
  { id: 'breeding', name: 'Breeding', free: false, beta: false },
  { id: 'feeding', name: 'Feeding', free: 'basic', beta: true },
  { id: 'financial', name: 'Financial', free: 'basic', beta: true }, // Now available in BETA with limitations
  { id: 'inventory', name: 'Inventory', free: 'basic', beta: true },
  { id: 'commercial', name: 'Commercial', free: false, beta: false },
  { id: 'reports', name: 'Reports', free: 'basic', beta: true },
  { id: 'analytics', name: 'Analytics', free: false, beta: false },
  { id: 'resources', name: 'Resources', free: true, beta: true },
  { id: 'compliance', name: 'Compliance', free: false, beta: false },
  { id: 'settings', name: 'Settings', free: true, beta: true }
];

interface IntegratedAppProps {
  onNavigate?: (path: string) => void;
}

const IntegratedApp: React.FC<IntegratedAppProps> = ({ onNavigate }) => {
  const theme = useTheme();

  // State management
  const [currentView, setCurrentView] = useState<'login' | 'register' | 'dashboard'>('login');
  const [user, setUser] = useState<any>(null);
  const [showPricing, setShowPricing] = useState(false);
  const [selectedModule, setSelectedModule] = useState<string>('');
  const [registrationData, setRegistrationData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    password: process.env.DEFAULT_PASSWORD || 'CHANGE_ME',
    confirmPassword: '',
    farmName: '',
    subscriptionTier: 'free'
  });
  const [loginData, setLoginData] = useState({
    username: '',
    password: process.env.DEFAULT_PASSWORD || 'CHANGE_ME'
  });

  // Authentication functions
  const handleLogin = async () => {
    // Simulate login with different user types
    const users = {
      'admin': { role: 'admin', subscription: 'enterprise', name: 'Administrator' },
      'Administrator': { role: 'admin', subscription: 'enterprise', name: 'Administrator' },
      'Demo': { role: 'demo', subscription: 'free', name: 'Demo User' },
      'beta': { role: 'beta', subscription: 'free', name: 'Beta User' },
      'BetaUser': { role: 'beta', subscription: 'free', name: 'Beta User' },
      'pro': { role: 'user', subscription: 'professional', name: 'Pro User' },
      'May Rakgama': { role: 'admin', subscription: 'enterprise', name: 'May Rakgama' }
    };

    const userData = users[loginData.username as keyof typeof users];
    if (userData && (loginData.password === 'Admin@123' || loginData.password === 'admin123' || loginData.password === '123' || loginData.password === 'beta123' || loginData.password === 'mayrakgama2024')) {
      setUser({
        username: loginData.username,
        ...userData
      });
      setCurrentView('dashboard');
    } else {
      alert('Invalid credentials. Try: admin/Admin@123, Demo/123, or BetaUser/beta123');
    }
  };

  const handleRegister = () => {
    setUser({
      username: registrationData.email,
      role: 'user',
      subscription: registrationData.subscriptionTier,
      name: `${registrationData.firstName} ${registrationData.lastName}`,
      farmName: registrationData.farmName
    });
    setCurrentView('dashboard');
  };

  const handleModuleClick = (moduleId: string) => {
    const module = modules.find(m => m.id === moduleId);
    if (!module) return;

    // Check access permissions
    const hasAccess = checkModuleAccess(module, user?.subscription || 'free');
    
    if (!hasAccess) {
      setSelectedModule(moduleId);
      setShowPricing(true);
    } else {
      // Navigate to module
      if (onNavigate) {
        onNavigate(`/${moduleId}`);
      }
    }
  };

  const checkModuleAccess = (module: any, subscription: string) => {
    if (subscription === 'enterprise') return true;
    if (subscription === 'professional' && module.free !== false) return true;
    if (subscription === 'free' && module.free === true) return true;
    return false;
  };

  const renderLoginForm = () => (
    <Card sx={{ maxWidth: 400, mx: 'auto', mt: 4 }}>
      <CardContent sx={{ p: 4 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <Typography variant="h4" sx={{ flexGrow: 1 }}>
            AgriIntel Login
          </Typography>
          <LanguageSelector size="small" />
        </Box>
        
        <TextField
          fullWidth
          label="Username"
          value={loginData.username}
          onChange={(e) => setLoginData({...loginData, username: e.target.value})}
          sx={{ mb: 2 }}
        />
        
        <TextField
          fullWidth
          type="password"
          label="Password"
          value={loginData.password}
          onChange={(e) => setLoginData({...loginData, password: e.target.value})}
          sx={{ mb: 3 }}
        />
        
        <Button
          fullWidth
          variant="contained"
          onClick={handleLogin}
          sx={{ mb: 2 }}
        >
          Sign In
        </Button>
        
        <Button
          fullWidth
          variant="text"
          onClick={() => setCurrentView('register')}
        >
          Create Account
        </Button>
        
        <Box sx={{ mt: 3, p: 2, bgcolor: alpha(theme.palette.info.main, 0.1), borderRadius: 1 }}>
          <Typography variant="caption" display="block">
            Demo Accounts:
          </Typography>
          <Typography variant="caption" display="block">
            Admin: admin / Admin@123
          </Typography>
          <Typography variant="caption" display="block">
            Demo: Demo / 123
          </Typography>
        </Box>
      </CardContent>
    </Card>
  );

  const renderRegistrationForm = () => (
    <Card sx={{ maxWidth: 500, mx: 'auto', mt: 4 }}>
      <CardContent sx={{ p: 4 }}>
        <Typography variant="h4" sx={{ mb: 3 }}>
          Create AgriIntel Account
        </Typography>
        
        <Grid container spacing={2}>
          <Grid item xs={6}>
            <TextField
              fullWidth
              label="First Name"
              value={registrationData.firstName}
              onChange={(e) => setRegistrationData({...registrationData, firstName: e.target.value})}
            />
          </Grid>
          <Grid item xs={6}>
            <TextField
              fullWidth
              label="Last Name"
              value={registrationData.lastName}
              onChange={(e) => setRegistrationData({...registrationData, lastName: e.target.value})}
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Email"
              type="email"
              value={registrationData.email}
              onChange={(e) => setRegistrationData({...registrationData, email: e.target.value})}
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Farm Name"
              value={registrationData.farmName}
              onChange={(e) => setRegistrationData({...registrationData, farmName: e.target.value})}
            />
          </Grid>
          <Grid item xs={6}>
            <TextField
              fullWidth
              type="password"
              label="Password"
              value={registrationData.password}
              onChange={(e) => setRegistrationData({...registrationData, password: e.target.value})}
            />
          </Grid>
          <Grid item xs={6}>
            <TextField
              fullWidth
              type="password"
              label="Confirm Password"
              value={registrationData.confirmPassword}
              onChange={(e) => setRegistrationData({...registrationData, confirmPassword: e.target.value})}
            />
          </Grid>
          <Grid item xs={12}>
            <FormControl fullWidth>
              <InputLabel>Subscription Plan</InputLabel>
              <Select
                value={registrationData.subscriptionTier}
                onChange={(e) => setRegistrationData({...registrationData, subscriptionTier: e.target.value})}
              >
                {subscriptionPlans.map(plan => (
                  <MenuItem key={plan.id} value={plan.id}>
                    {plan.name} - {plan.price}/{plan.period}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
        </Grid>
        
        <Button
          fullWidth
          variant="contained"
          onClick={handleRegister}
          sx={{ mt: 3, mb: 2 }}
        >
          Create Account
        </Button>
        
        <Button
          fullWidth
          variant="text"
          onClick={() => setCurrentView('login')}
        >
          Back to Login
        </Button>
      </CardContent>
    </Card>
  );

  const renderDashboard = () => (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Box>
          <Typography variant="h3" sx={{ mb: 1 }}>
            Welcome to AgriIntel, {user?.name}
          </Typography>
          <Typography variant="subtitle1" color="text.secondary">
            {user?.farmName && `${user.farmName} • `}
            {user?.subscription.charAt(0).toUpperCase() + user?.subscription.slice(1)} Plan
          </Typography>
        </Box>

        <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
          <LanguageSelector size="small" />
          <Button
            variant="outlined"
            startIcon={<PaletteIcon />}
            onClick={() => alert('Theme settings coming soon!')}
          >
            Themes
          </Button>
          <Button
            variant="contained"
            onClick={() => setUser(null)}
          >
            Logout
          </Button>
        </Box>
      </Box>

      {/* Subscription Status */}
      {user?.subscription === 'free' && (
        <Alert
          severity="info"
          sx={{ mb: 3 }}
          action={
            <Button color="inherit" size="small" onClick={() => setShowPricing(true)}>
              Upgrade
            </Button>
          }
        >
          You're on the Free Beta plan. Upgrade to unlock all features!
        </Alert>
      )}

      {/* Module Grid */}
      <Grid container spacing={3}>
        {modules.map((module) => {
          const hasAccess = checkModuleAccess(module, user?.subscription || 'free');
          const isLimited = module.limit && user?.subscription === 'free';

          return (
            <Grid item xs={12} sm={6} md={4} key={module.id}>
              <Card
                component={motion.div}
                whileHover={{ y: hasAccess ? -5 : 0 }}
                sx={{
                  height: '100%',
                  cursor: hasAccess ? 'pointer' : 'default',
                  opacity: hasAccess ? 1 : 0.6,
                  position: 'relative',
                  background: hasAccess
                    ? `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)}, ${alpha(theme.palette.secondary.main, 0.1)})`
                    : alpha(theme.palette.grey[500], 0.1)
                }}
                onClick={() => handleModuleClick(module.id)}
              >
                <CardContent>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                    <Typography variant="h6">
                      {module.name}
                    </Typography>
                    {!hasAccess && <LockIcon color="disabled" />}
                    {isLimited && <Chip label={`Max ${module.limit}`} size="small" />}
                  </Box>

                  <Typography variant="body2" color="text.secondary">
                    {hasAccess
                      ? `Access to ${module.name.toLowerCase()} management features`
                      : `Upgrade to access ${module.name.toLowerCase()} features`
                    }
                  </Typography>

                  {!hasAccess && (
                    <Button
                      size="small"
                      variant="outlined"
                      startIcon={<StarIcon />}
                      sx={{ mt: 2 }}
                      onClick={(e) => {
                        e.stopPropagation();
                        setSelectedModule(module.id);
                        setShowPricing(true);
                      }}
                    >
                      Upgrade
                    </Button>
                  )}
                </CardContent>
              </Card>
            </Grid>
          );
        })}
      </Grid>
    </Box>
  );

  const renderPricingDialog = () => (
    <Dialog
      open={showPricing}
      onClose={() => setShowPricing(false)}
      maxWidth="lg"
      fullWidth
    >
      <DialogTitle>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h5">
            Choose Your AgriIntel Plan
            {selectedModule && (
              <Typography variant="subtitle2" color="text.secondary">
                Unlock {selectedModule.charAt(0).toUpperCase() + selectedModule.slice(1)} features
              </Typography>
            )}
          </Typography>
          <Button onClick={() => setShowPricing(false)}>
            <CloseIcon />
          </Button>
        </Box>
      </DialogTitle>

      <DialogContent>
        <Grid container spacing={3}>
          {subscriptionPlans.map((plan) => (
            <Grid item xs={12} md={4} key={plan.id}>
              <Card
                sx={{
                  height: '100%',
                  position: 'relative',
                  border: plan.popular ? `2px solid ${plan.color}` : '1px solid',
                  borderColor: plan.popular ? plan.color : 'divider'
                }}
              >
                {plan.popular && (
                  <Chip
                    label="Most Popular"
                    color="primary"
                    sx={{
                      position: 'absolute',
                      top: -10,
                      left: '50%',
                      transform: 'translateX(-50%)',
                      zIndex: 1
                    }}
                  />
                )}

                <CardContent sx={{ p: 3 }}>
                  <Typography variant="h5" sx={{ mb: 1, color: plan.color }}>
                    {plan.name}
                  </Typography>

                  <Box sx={{ display: 'flex', alignItems: 'baseline', mb: 2 }}>
                    <Typography variant="h3" sx={{ color: plan.color }}>
                      {plan.price}
                    </Typography>
                    <Typography variant="body1" sx={{ ml: 1 }}>
                      /{plan.period}
                    </Typography>
                  </Box>

                  <List dense>
                    {plan.features.map((feature, index) => (
                      <ListItem key={index} sx={{ px: 0 }}>
                        <ListItemIcon sx={{ minWidth: 32 }}>
                          <CheckIcon color="success" fontSize="small" />
                        </ListItemIcon>
                        <ListItemText primary={feature} />
                      </ListItem>
                    ))}
                  </List>

                  <Button
                    fullWidth
                    variant={plan.popular ? "contained" : "outlined"}
                    sx={{
                      mt: 2,
                      backgroundColor: plan.popular ? plan.color : 'transparent',
                      borderColor: plan.color,
                      color: plan.popular ? 'white' : plan.color,
                      '&:hover': {
                        backgroundColor: plan.popular ? plan.color : alpha(plan.color, 0.1)
                      }
                    }}
                    onClick={() => {
                      // Handle subscription upgrade
                      setUser({...user, subscription: plan.id});
                      setShowPricing(false);
                    }}
                  >
                    {user?.subscription === plan.id ? 'Current Plan' : 'Choose Plan'}
                  </Button>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </DialogContent>
    </Dialog>
  );

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <AnimatePresence mode="wait">
        {currentView === 'login' && (
          <motion.div
            key="login"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 20 }}
          >
            {renderLoginForm()}
          </motion.div>
        )}

        {currentView === 'register' && (
          <motion.div
            key="register"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 20 }}
          >
            {renderRegistrationForm()}
          </motion.div>
        )}

        {currentView === 'dashboard' && user && (
          <motion.div
            key="dashboard"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
          >
            {renderDashboard()}
          </motion.div>
        )}
      </AnimatePresence>

      {renderPricingDialog()}
    </Container>
  );
};

export default IntegratedApp;
