<?xml version="1.0" encoding="UTF-8"?>
<svg width="240" height="80" viewBox="0 0 240 80" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- AgriIntel Gradient -->
    <linearGradient id="agriGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#22C55E;stop-opacity:1" />
      <stop offset="25%" style="stop-color:#3B82F6;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#A855F7;stop-opacity:1" />
      <stop offset="75%" style="stop-color:#F59E0B;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#EF4444;stop-opacity:1" />
    </linearGradient>

    <!-- Icon Gradient -->
    <linearGradient id="iconGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#059669;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#0D9488;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0F766E;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- Farm/Agriculture Icon -->
  <g transform="translate(10, 15)">
    <!-- Barn/Farm Building -->
    <path d="M5 35 L15 25 L25 35 L25 45 L5 45 Z" fill="url(#iconGradient)" stroke="#065F46" stroke-width="1"/>
    <!-- Roof -->
    <path d="M3 35 L15 23 L27 35" fill="none" stroke="#065F46" stroke-width="2" stroke-linecap="round"/>
    <!-- Door -->
    <rect x="12" y="38" width="6" height="7" fill="#065F46"/>
    <!-- Windows -->
    <rect x="7" y="32" width="4" height="4" fill="#FEF3C7"/>
    <rect x="19" y="32" width="4" height="4" fill="#FEF3C7"/>
    <!-- Livestock (simplified cattle) -->
    <ellipse cx="35" cy="42" rx="8" ry="4" fill="#8B5CF6" opacity="0.8"/>
    <ellipse cx="35" cy="40" rx="6" ry="3" fill="#A855F7"/>
  </g>

  <!-- AgriIntel Text -->
  <text x="65" y="35" style="font-family: 'Segoe UI', Arial, sans-serif; font-weight: 700; font-size: 28px; fill: url(#agriGradient);">AgriIntel</text>

  <!-- Tagline -->
  <text x="67" y="52" style="font-family: 'Segoe UI', Arial, sans-serif; font-weight: 500; font-size: 11px; fill: #6B7280; letter-spacing: 1.5px;">SMART FARMING • SMARTER DECISIONS</text>

  <!-- Decorative Elements -->
  <circle cx="220" cy="25" r="3" fill="#22C55E" opacity="0.6"/>
  <circle cx="225" cy="35" r="2" fill="#3B82F6" opacity="0.6"/>
  <circle cx="215" cy="40" r="2.5" fill="#F59E0B" opacity="0.6"/>
</svg>
