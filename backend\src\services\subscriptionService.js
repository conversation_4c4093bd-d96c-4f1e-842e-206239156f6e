/**
 * Subscription Service
 *
 * Handles subscription management, billing, and feature access control
 * Note: Using mock data for now - database integration to be added later
 */

const logger = require('../utils/logger');

class SubscriptionService {
  
  /**
   * Initialize default subscription plans
   */
  static async initializeDefaultPlans() {
    try {
      // Mock initialization - plans are hardcoded for now
      logger.info('Default subscription plans initialized successfully (mock)');
      return true;
    } catch (error) {
      logger.error('Error initializing subscription plans:', error);
      throw error;
    }
  }

  /**
   * Get all available subscription plans
   */
  static async getAvailablePlans() {
    try {
      // Mock subscription plans
      const plans = [
        {
          _id: 'beta',
          name: 'beta',
          displayName: 'Beta (Free)',
          description: 'Essential record-keeping suite with government data integration',
          price: { monthly: 0, yearly: 0, currency: 'ZAR' },
          features: {
            maxAnimals: 50,
            maxUsers: 1,
            historicalDataMonths: 6,
            maxReports: 5,
            maxAlerts: 5,
            aiAgentMode: false,
            advancedAnalytics: false,
            financialIntelligence: false,
            apiAccess: false,
            prioritySupport: false,
            customReports: false,
            dataExport: { formats: ['csv'] },
            governmentIntegration: true,
            weatherIntegration: true,
            mobileApp: true
          },
          sortOrder: 1,
          isActive: true
        },
        {
          _id: 'professional',
          name: 'professional',
          displayName: 'Professional',
          description: 'Full features for farms up to 200 animals with advanced analytics',
          price: { monthly: 299, yearly: 2990, currency: 'ZAR' },
          features: {
            maxAnimals: 200,
            maxUsers: 3,
            historicalDataMonths: -1,
            maxReports: 50,
            maxAlerts: -1,
            aiAgentMode: false,
            advancedAnalytics: true,
            financialIntelligence: true,
            apiAccess: true,
            prioritySupport: false,
            customReports: true,
            dataExport: { formats: ['csv', 'excel', 'pdf'] },
            governmentIntegration: true,
            weatherIntegration: true,
            mobileApp: true
          },
          sortOrder: 2,
          isActive: true
        }
      ];

      return plans;
    } catch (error) {
      logger.error('Error getting subscription plans:', error);
      throw error;
    }
  }

  /**
   * Get user's current subscription
   */
  static async getUserSubscription(userId) {
    try {
      // Mock user subscription - return beta subscription for demo
      const plans = await this.getAvailablePlans();
      const betaPlan = plans.find(p => p.name === 'beta');

      const trialEndDate = new Date();
      trialEndDate.setDate(trialEndDate.getDate() + 30);

      const subscription = {
        _id: `sub_${userId}`,
        userId,
        planId: betaPlan,
        status: 'trial',
        startDate: new Date(),
        endDate: trialEndDate,
        trialEndDate,
        billingCycle: 'monthly',
        usage: {
          currentAnimals: 5,
          currentUsers: 1,
          monthlyAlerts: 2,
          monthlyReports: 1,
          lastResetDate: new Date()
        }
      };

      return subscription;
    } catch (error) {
      logger.error('Error getting user subscription:', error);
      throw error;
    }
  }

  /**
   * Create beta subscription for new users
   */
  static async createBetaSubscription(userId) {
    try {
      // Mock creation - just return the subscription
      return await this.getUserSubscription(userId);
    } catch (error) {
      logger.error('Error creating beta subscription:', error);
      throw error;
    }
  }

  /**
   * Check if user can access a feature
   */
  static async canAccessFeature(userId, feature, currentUsage = {}) {
    try {
      const subscription = await this.getUserSubscription(userId);
      if (!subscription || !subscription.planId) {
        return { allowed: false, reason: 'No active subscription' };
      }

      const plan = subscription.planId;
      const features = plan.features;

      // Check specific feature limits
      switch (feature) {
        case 'add_animal':
          if (features.maxAnimals !== -1 && currentUsage.animals >= features.maxAnimals) {
            return {
              allowed: false,
              reason: `Animal limit reached (${features.maxAnimals})`,
              upgradeRequired: true
            };
          }
          break;

        case 'create_report':
          if (features.maxReports !== -1 && currentUsage.monthlyReports >= features.maxReports) {
            return {
              allowed: false,
              reason: `Monthly report limit reached (${features.maxReports})`,
              upgradeRequired: true
            };
          }
          break;

        case 'ai_agent':
          if (!features.aiAgentMode) {
            return {
              allowed: false,
              reason: 'AI Agent mode requires premium subscription',
              upgradeRequired: true
            };
          }
          break;

        case 'advanced_analytics':
          if (!features.advancedAnalytics) {
            return {
              allowed: false,
              reason: 'Advanced analytics requires premium subscription',
              upgradeRequired: true
            };
          }
          break;

        case 'financial_intelligence':
          if (!features.financialIntelligence) {
            return {
              allowed: false,
              reason: 'Financial intelligence requires premium subscription',
              upgradeRequired: true
            };
          }
          break;

        default:
          return { allowed: true };
      }

      return { allowed: true };
    } catch (error) {
      logger.error('Error checking feature access:', error);
      return { allowed: false, reason: 'Error checking permissions' };
    }
  }

  /**
   * Track feature usage
   */
  static async trackFeatureUsage(userId, feature, action, metadata = {}) {
    try {
      // Mock tracking - just log for now
      logger.debug(`Feature usage tracked: ${userId} - ${feature}:${action}`);
      return true;
    } catch (error) {
      logger.error('Error tracking feature usage:', error);
    }
  }

  /**
   * Update usage statistics
   */
  static async updateUsage(userId, usageType, increment = 1) {
    try {
      // Mock update - return updated subscription
      const subscription = await this.getUserSubscription(userId);
      return subscription;
    } catch (error) {
      logger.error('Error updating usage:', error);
      throw error;
    }
  }

  /**
   * Upgrade user subscription
   */
  static async upgradeSubscription(userId, planName, billingCycle = 'monthly') {
    try {
      // Mock upgrade - return updated subscription
      const plans = await this.getAvailablePlans();
      const newPlan = plans.find(p => p.name === planName);

      if (!newPlan) {
        throw new Error('Subscription plan not found');
      }

      const subscription = await this.getUserSubscription(userId);
      subscription.planId = newPlan;
      subscription.status = 'active';
      subscription.billingCycle = billingCycle;

      logger.info(`Subscription upgraded for user ${userId} to ${planName} (mock)`);
      return subscription;
    } catch (error) {
      logger.error('Error upgrading subscription:', error);
      throw error;
    }
  }

  /**
   * Cancel subscription
   */
  static async cancelSubscription(userId, reason, feedback) {
    try {
      // Mock cancellation
      const subscription = await this.getUserSubscription(userId);
      subscription.status = 'cancelled';

      logger.info(`Subscription cancelled for user ${userId} (mock)`);
      return subscription;
    } catch (error) {
      logger.error('Error cancelling subscription:', error);
      throw error;
    }
  }

  /**
   * Update conversion metrics
   */
  static async updateConversionMetrics(userId, feature, action) {
    try {
      // Mock metrics
      const metrics = {
        userId,
        engagementScore: 45,
        conversionProbability: 0.3,
        keyMetrics: {
          daysActive: 5,
          featuresUsed: 8,
          recordsCreated: 12,
          limitsHit: 2,
          previewsViewed: 6
        }
      };

      return metrics;
    } catch (error) {
      logger.error('Error updating conversion metrics:', error);
    }
  }

  /**
   * Get conversion prospects
   */
  static async getConversionProspects(limit = 50) {
    try {
      // Mock prospects data
      return [
        {
          userId: 'user1',
          engagementScore: 85,
          conversionProbability: 0.8,
          keyMetrics: { limitsHit: 5, previewsViewed: 10 }
        },
        {
          userId: 'user2',
          engagementScore: 72,
          conversionProbability: 0.6,
          keyMetrics: { limitsHit: 3, previewsViewed: 8 }
        }
      ];
    } catch (error) {
      logger.error('Error getting conversion prospects:', error);
      throw error;
    }
  }

  /**
   * Get province data
   */
  static async getProvinceData(province) {
    try {
      // Mock province data
      const provinceData = {
        'gauteng': {
          name: 'Gauteng',
          totalFarms: 1250,
          averageHerdSize: 85,
          primaryLivestock: ['cattle', 'sheep', 'goats'],
          weatherPattern: 'summer_rainfall'
        },
        'western-cape': {
          name: 'Western Cape',
          totalFarms: 2100,
          averageHerdSize: 120,
          primaryLivestock: ['sheep', 'cattle', 'ostriches'],
          weatherPattern: 'winter_rainfall'
        }
      };

      return provinceData[province] || provinceData['gauteng'];
    } catch (error) {
      logger.error('Error getting province data:', error);
      throw error;
    }
  }
}

module.exports = SubscriptionService;
