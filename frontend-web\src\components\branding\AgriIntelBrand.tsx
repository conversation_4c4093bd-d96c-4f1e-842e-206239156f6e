import React from 'react';
import { Box, Typography, useTheme, alpha } from '@mui/material';
import { useThemeContext } from '../../contexts/ThemeContext';
import { Agriculture, TrendingUp, Eco } from '@mui/icons-material';
import '../../styles/components/agriintel-branding.css';

interface AgriIntelBrandProps {
  variant?: 'full' | 'compact' | 'logo-only';
  size?: 'small' | 'medium' | 'large' | 'extra-large';
  showSlogan?: boolean;
  color?: 'primary' | 'secondary' | 'white' | 'inherit';
  orientation?: 'horizontal' | 'vertical';
  animated?: boolean;
  sx?: any;
}

const AgriIntelBrand: React.FC<AgriIntelBrandProps> = ({
  variant = 'full',
  size = 'medium',
  showSlogan = true,
  color = 'primary',
  orientation = 'horizontal',
  animated = false,
  sx = {}
}) => {
  const theme = useTheme();
  const { currentColor, availableColors } = useThemeContext();

  const themeColor = availableColors[currentColor];

  // Size configurations
  const sizeConfig = {
    small: {
      logoSize: 32,
      fontSize: '1.2rem',
      sloganSize: '0.7rem',
      spacing: 1
    },
    medium: {
      logoSize: 40,
      fontSize: '1.8rem',
      sloganSize: '0.8rem',
      spacing: 1.5
    },
    large: {
      logoSize: 56,
      fontSize: '2.5rem',
      sloganSize: '1rem',
      spacing: 2
    },
    'extra-large': {
      logoSize: 72,
      fontSize: '3.2rem',
      sloganSize: '1.2rem',
      spacing: 2.5
    }
  };

  const config = sizeConfig[size];

  // Color configurations
  const getColorStyle = () => {
    switch (color) {
      case 'white':
        return {
          primary: '#FFFFFF',
          secondary: alpha('#FFFFFF', 0.8),
          gradient: 'linear-gradient(135deg, #FFFFFF 0%, rgba(255,255,255,0.9) 100%)'
        };
      case 'inherit':
        return {
          primary: 'inherit',
          secondary: 'inherit',
          gradient: 'none'
        };
      default:
        return {
          primary: themeColor?.primary || theme.palette.primary.main,
          secondary: themeColor?.secondary || theme.palette.secondary.main,
          gradient: `linear-gradient(135deg,
            ${themeColor?.primary || '#22C55E'} 0%,
            ${themeColor?.secondary || '#3B82F6'} 25%,
            #A855F7 50%,
            #F59E0B 75%,
            #EF4444 100%)`
        };
    }
  };

  const colorStyle = getColorStyle();

  // AgriIntel Logo SVG Component
  const AgriIntelLogo = ({ size: logoSize }: { size: number }) => (
    <svg width={logoSize} height={logoSize * 0.8} viewBox="0 0 60 48" xmlns="http://www.w3.org/2000/svg" className="agri-logo">
      <defs>
        <linearGradient id={`agriGradient-${logoSize}`} x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" className="agri-logo-gradient-primary" />
          <stop offset="25%" className="agri-logo-gradient-secondary" />
          <stop offset="50%" className="agri-logo-gradient-purple" />
          <stop offset="75%" className="agri-logo-gradient-amber" />
          <stop offset="100%" className="agri-logo-gradient-red" />
        </linearGradient>

        <linearGradient id={`iconGradient-${logoSize}`} x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" className="agri-logo-icon-green" />
          <stop offset="50%" className="agri-logo-icon-teal" />
          <stop offset="100%" className="agri-logo-icon-teal-dark" />
        </linearGradient>
      </defs>

      {/* Farm Building */}
      <g transform="translate(5, 8)">
        <path d="M5 25 L15 15 L25 25 L25 35 L5 35 Z" fill={`url(#iconGradient-${logoSize})`} stroke="var(--agri-forest)" strokeWidth="1"/>
        <path d="M3 25 L15 13 L27 25" fill="none" stroke="var(--agri-forest)" strokeWidth="1.5" strokeLinecap="round"/>
        <rect x="12" y="28" width="6" height="7" fill="var(--agri-forest)"/>
        <rect x="7" y="22" width="4" height="4" fill="var(--agri-cream)"/>
        <rect x="19" y="22" width="4" height="4" fill="var(--agri-cream)"/>

        {/* Livestock */}
        <ellipse cx="35" cy="32" rx="8" ry="4" fill="var(--agri-accent-purple)" opacity="0.8"/>
        <ellipse cx="35" cy="30" rx="6" ry="3" fill="var(--agri-accent-purple)"/>
      </g>
    </svg>
  );

  // Animation styles
  const animationStyles = animated ? {
    '@keyframes agriShimmer': {
      '0%': { backgroundPosition: '-200% center' },
      '100%': { backgroundPosition: '200% center' }
    },
    animation: 'agriShimmer 3s ease-in-out infinite',
    backgroundSize: '200% 100%'
  } : {};

  // Brand text styles
  const getBrandTextStyle = () => {
    const baseStyle = {
      fontWeight: 'bold',
      letterSpacing: '-0.02em',
      fontFamily: '"Segoe UI", "Roboto", "Arial", sans-serif',
      ...animationStyles
    };

    switch (color) {
      case 'primary':
        return {
          ...baseStyle,
          background: colorStyle.gradient,
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          backgroundClip: 'text'
        };
      case 'white':
        return {
          ...baseStyle,
          color: '#FFFFFF',
          textShadow: `0 2px 4px ${alpha('#000000', 0.3)}`
        };
      case 'inherit':
        return {
          ...baseStyle,
          color: 'inherit'
        };
      default:
        return {
          ...baseStyle,
          color: colorStyle.primary
        };
    }
  };

  // Slogan text styles
  const getSloganTextStyle = () => {
    const baseStyle = {
      fontWeight: 500,
      fontFamily: '"Segoe UI", "Roboto", "Arial", sans-serif',
      letterSpacing: '0.5px'
    };

    switch (color) {
      case 'primary':
        return {
          ...baseStyle,
          background: `linear-gradient(135deg, ${alpha(colorStyle.primary, 0.8)}, ${alpha(colorStyle.secondary || '#3B82F6', 0.8)})`,
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          backgroundClip: 'text'
        };
      case 'white':
        return {
          ...baseStyle,
          color: alpha('#FFFFFF', 0.9),
          textShadow: `0 1px 2px ${alpha('#000000', 0.3)}`
        };
      case 'inherit':
        return {
          ...baseStyle,
          color: 'inherit'
        };
      default:
        return {
          ...baseStyle,
          color: theme.palette.text.secondary
        };
    }
  };

  const brandTextStyle = getBrandTextStyle();
  const sloganTextStyle = getSloganTextStyle();

  if (variant === 'logo-only') {
    return (
      <Box sx={{ display: 'flex', alignItems: 'center', ...sx }}>
        <AgriIntelLogo size={config.logoSize} />
      </Box>
    );
  }

  if (variant === 'compact') {
    return (
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          flexDirection: orientation === 'vertical' ? 'column' : 'row',
          textAlign: orientation === 'vertical' ? 'center' : 'left',
          gap: config.spacing,
          ...sx
        }}
      >
        <AgriIntelLogo size={config.logoSize} />
        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: orientation === 'vertical' ? 'center' : 'flex-start' }}>
          <Typography
            variant="h4"
            component="span"
            sx={{
              fontSize: config.fontSize,
              ...brandTextStyle
            }}
          >
            AgriIntel
          </Typography>
          {showSlogan && (
            <Typography
              variant="caption"
              component="span"
              sx={{
                fontSize: config.sloganSize,
                ...sloganTextStyle
              }}
            >
              Smart Farming, Smarter Decisions
            </Typography>
          )}
        </Box>
      </Box>
    );
  }

  // Full variant
  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: orientation === 'vertical' ? 'column' : 'row',
        alignItems: orientation === 'vertical' ? 'center' : 'center',
        textAlign: orientation === 'vertical' ? 'center' : 'left',
        gap: config.spacing,
        ...sx
      }}
    >
      <AgriIntelLogo size={config.logoSize} />
      <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: orientation === 'vertical' ? 'center' : 'flex-start' }}>
        <Typography
          variant="h1"
          component="h1"
          sx={{
            fontSize: config.fontSize,
            lineHeight: 1.1,
            ...brandTextStyle
          }}
        >
          AgriIntel
        </Typography>
        {showSlogan && (
          <Typography
            variant="subtitle1"
            component="p"
            sx={{
              fontSize: config.sloganSize,
              lineHeight: 1.2,
              mt: 0.5,
              ...sloganTextStyle
            }}
          >
            Smart Farming, Smarter Decisions
          </Typography>
        )}
      </Box>
    </Box>
  );
};

export default AgriIntelBrand;
